@file:OptIn(ExperimentalMaterial3Api::class)

package com.example.myapplication.screens

import android.Manifest
import android.content.pm.PackageManager
import android.util.Log
import android.util.Size
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import com.example.myapplication.model.EmployeeData
import com.example.myapplication.utils.QRCodeAnalyzer
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionStatus
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.concurrent.Executors
import kotlin.text.isNotEmpty

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun QRScannerScreen(
    employeeData: EmployeeData,
    onQRCodeScanned: (String) -> Unit,
    onBatchIdScanned: (String) -> Unit = {},
    onSettingsClick: () -> Unit,
    onUpdateEmployeeInfo: (employeeName: String, companyName: String) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }
    val coroutineScope = rememberCoroutineScope()

    var showScanner by remember { mutableStateOf(true) }
    var jsonOutput by remember { mutableStateOf("") }
    var hasCameraPermission by remember { mutableStateOf(false) }
    var scannedBatchId by remember { mutableStateOf("") }

    // 二维码格式验证状态
    var qrFormatValid by remember { mutableStateOf(false) }
    var qrErrorMessage by remember { mutableStateOf("") }
    var parsedCompanyCode by remember { mutableStateOf("") }
    var parsedBatchId by remember { mutableStateOf("") }

    // 员工信息快速编辑对话框
    var showEmployeeDialog by remember { mutableStateOf(false) }
    var tempEmployeeName by remember { mutableStateOf(employeeData.employeeName) }
    var tempCompanyName by remember { mutableStateOf(employeeData.companyName) }

    // 相机权限处理 - 增强Android 6.0兼容性
    val cameraPermissionState = rememberPermissionState(Manifest.permission.CAMERA)
    var permissionRequested by remember { mutableStateOf(false) }
    var showPermissionRationale by remember { mutableStateOf(false) }

    LaunchedEffect(cameraPermissionState.status) {
        when {
            cameraPermissionState.status.isGranted -> {
                hasCameraPermission = true
                showPermissionRationale = false
            }
            cameraPermissionState.status is PermissionStatus.Denied &&
            (cameraPermissionState.status as PermissionStatus.Denied).shouldShowRationale -> {
                // 用户之前拒绝了权限，需要显示说明
                hasCameraPermission = false
                showPermissionRationale = true
            }
            !permissionRequested -> {
                // 首次请求权限
                permissionRequested = true
                cameraPermissionState.launchPermissionRequest()
            }
            else -> {
                // 权限被永久拒绝
                hasCameraPermission = false
                showPermissionRationale = false
            }
        }
    }

    // 快速编辑员工信息对话框
    if (showEmployeeDialog) {
        Dialog(onDismissRequest = { showEmployeeDialog = false }) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "修改员工信息",
                        style = MaterialTheme.typography.titleLarge
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 员工姓名输入
                    TextField(
                        value = tempEmployeeName,
                        onValueChange = { tempEmployeeName = it },
                        label = { Text("员工姓名") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 不再需要公司名称输入框

                    Spacer(modifier = Modifier.height(16.dp))

                    // 按钮行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Button(
                            onClick = { showEmployeeDialog = false },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant,
                                contentColor = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        ) {
                            Text("取消")
                        }

                        Button(
                            onClick = {
                                if (tempEmployeeName.isNotBlank()) {
                                    // 只传递员工名称，公司名称参数保留为空字符串以保持接口兼容性
                                    onUpdateEmployeeInfo(tempEmployeeName, "")
                                    showEmployeeDialog = false
                                }
                            },
                            enabled = tempEmployeeName.isNotBlank()
                        ) {
                            Text("保存")
                        }
                    }
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("扫描批次二维码") },
                navigationIcon = {
                    // 左上角服务器设置按钮
                    IconButton(onClick = onSettingsClick) {
                        Icon(Icons.Default.Menu, contentDescription = "服务器设置")
                    }
                },
                actions = {
                    // 显示员工信息编辑按钮
                    IconButton(onClick = {
                        // 初始化临时变量
                        tempEmployeeName = employeeData.employeeName
                        tempCompanyName = employeeData.companyName
                        showEmployeeDialog = true
                    }) {
                        Icon(Icons.Default.Edit, contentDescription = "编辑信息")
                    }
                }
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 显示当前员工和公司信息
            if (employeeData.employeeName.isNotBlank()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "员工: ${employeeData.employeeName}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }

            if (showScanner) {
                if (hasCameraPermission) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp)
                            .align(Alignment.CenterHorizontally),
                    ) {
                        // 相机预览
                        AndroidView(
                            factory = { ctx ->
                                val previewView = PreviewView(ctx).apply {
                                    this.scaleType = PreviewView.ScaleType.FILL_CENTER
                                }

                                coroutineScope.launch {
                                    val cameraProvider = cameraProviderFuture.get()
                                    val preview = Preview.Builder().build().also {
                                        it.setSurfaceProvider(previewView.surfaceProvider)
                                    }

                                    val imageAnalysis = ImageAnalysis.Builder()
                                        .setTargetResolution(Size(1280, 720))
                                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                                        .build()
                                        .also { analysis ->
                                            analysis.setAnalyzer(cameraExecutor, QRCodeAnalyzer { qrContent ->
                                                onQRCodeScanned(qrContent)

                                                // 保存原始二维码内容
                                                val trimmedQrContent = qrContent.trim()
                                                Log.d("QRScannerScreen", "扫描到二维码: $trimmedQrContent")
                                                scannedBatchId = trimmedQrContent

                                                // 检查二维码格式
                                                var isValidFormat = false
                                                var displayCompanyCode = ""
                                                var displayBatchId = ""
                                                var errorMessage = ""

                                                try {
                                                    // 如果包含分隔符"|"，则尝试分离客户代码和批次ID
                                                    if (trimmedQrContent.contains("|")) {
                                                        val parts = trimmedQrContent.split("|", limit = 2)
                                                        if (parts.size == 2) {
                                                            // 设置用于UI显示的值
                                                            displayCompanyCode = parts[0]
                                                            displayBatchId = parts[1]
                                                            isValidFormat = true

                                                            // 记录解析结果
                                                            Log.d("QRScannerScreen", "二维码解析成功: 客户代码=$displayCompanyCode, 批次ID=$displayBatchId")
                                                        } else {
                                                            errorMessage = "二维码格式错误：分隔后的部分不足"
                                                            Log.d("QRScannerScreen", errorMessage)
                                                        }
                                                    } else {
                                                        errorMessage = "二维码格式错误：缺少分隔符'|'"
                                                        Log.d("QRScannerScreen", errorMessage)
                                                    }
                                                } catch (e: Exception) {
                                                    errorMessage = "二维码解析出错：${e.message}"
                                                    Log.e("QRScannerScreen", "解析二维码内容出错", e)
                                                }

                                                // 设置状态变量
                                                qrFormatValid = isValidFormat
                                                qrErrorMessage = errorMessage
                                                parsedCompanyCode = displayCompanyCode
                                                parsedBatchId = displayBatchId

                                                // 创建JSON输出预览（仅用于调试）
                                                val jsonObject = JSONObject().apply {
                                                    put("employee", employeeData.employeeName)
                                                    put("qrContent", trimmedQrContent)
                                                    put("格式是否有效", isValidFormat)
                                                    put("错误信息", errorMessage)
                                                    put("解析的客户代码", displayCompanyCode)
                                                    put("解析的批次ID", displayBatchId)
                                                }

                                                jsonOutput = jsonObject.toString(4)
                                                showScanner = false

                                                // 停止扫描
                                                cameraProvider.unbindAll()
                                            })
                                        }

                                    // 清除之前的绑定
                                    cameraProvider.unbindAll()

                                    // 绑定相机用例
                                    val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
                                    cameraProvider.bindToLifecycle(
                                        lifecycleOwner,
                                        cameraSelector,
                                        preview,
                                        imageAnalysis
                                    )
                                }

                                previewView
                            },
                            modifier = Modifier.fillMaxSize()
                        )

                        // 扫描框指示
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(64.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Card(
                                modifier = Modifier
                                    .size(200.dp)
                                    .align(Alignment.Center),
                                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.0f))
                            ) { }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "请将批次二维码对准扫描框",
                        style = MaterialTheme.typography.bodyMedium
                    )
                } else {
                    // 没有相机权限的提示 - 增强Android 6.0兼容性
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Icon(
                                Icons.Default.Settings,
                                contentDescription = "相机权限",
                                modifier = Modifier.size(48.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.height(16.dp))

                            if (showPermissionRationale) {
                                Text(
                                    text = "相机权限说明",
                                    style = MaterialTheme.typography.titleMedium,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "此应用需要相机权限来扫描二维码。请允许相机权限以继续使用扫描功能。",
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Button(onClick = {
                                    showPermissionRationale = false
                                    cameraPermissionState.launchPermissionRequest()
                                }) {
                                    Text("授予权限")
                                }
                            } else if (cameraPermissionState.status is PermissionStatus.Denied &&
                                      !(cameraPermissionState.status as PermissionStatus.Denied).shouldShowRationale &&
                                      permissionRequested) {
                                // 权限被永久拒绝
                                Text(
                                    text = "相机权限被拒绝",
                                    style = MaterialTheme.typography.titleMedium,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "请在系统设置中手动开启相机权限，然后返回应用重试。",
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Button(onClick = {
                                    // 重新检查权限状态
                                    permissionRequested = false
                                }) {
                                    Text("重新检查权限")
                                }
                            } else {
                                Text(
                                    text = "需要相机权限才能扫描二维码",
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Button(onClick = { cameraPermissionState.launchPermissionRequest() }) {
                                    Text("请求权限")
                                }
                            }
                        }
                    }
                }
            } else {
                // 显示扫描结果，简洁样式
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.Start
                    ) {
                        Text(
                            text = "扫描结果",
                            style = MaterialTheme.typography.titleLarge,
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "员工：${employeeData.employeeName}",
                            style = MaterialTheme.typography.bodyLarge
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 根据二维码格式的有效性显示不同的内容
                        if (qrFormatValid) {
                            // 显示解析后的客户代码和批次ID
                            Text(
                                text = "客户代码：$parsedCompanyCode",
                                style = MaterialTheme.typography.bodyLarge
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = "批次ID：$parsedBatchId",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        } else {
                            // 显示错误提示
                            Text(
                                text = qrErrorMessage,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.error
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = "请使用正确格式的二维码：客户代码|批次ID",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "原始二维码：$scannedBatchId",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Button(
                        onClick = {
                            showScanner = true
                            // 清除上一次扫描的批次ID
                            scannedBatchId = ""
                            jsonOutput = ""
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("继续扫描")
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Button(
                        onClick = {
                            // 只有在二维码格式有效时才调用回调函数
                            if (qrFormatValid) {
                                onBatchIdScanned(scannedBatchId)
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = qrFormatValid // 只有在二维码格式有效时才启用按钮
                    ) {
                        Text("进入流程处理")
                    }
                }
            }
        }
    }
}