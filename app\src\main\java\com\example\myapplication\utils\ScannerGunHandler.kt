package com.example.myapplication.utils

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 扫码枪输入处理器
 * 专门处理红外线扫码枪的键盘楔入模式输入
 */
class ScannerGunHandler {
    companion object {
        private const val TAG = "ScannerGunHandler"
        private const val INPUT_TIMEOUT_MS = 3000L // 3秒超时
        private const val MIN_SCAN_LENGTH = 3 // 最小扫码长度
        private const val MAX_SCAN_LENGTH = 200 // 最大扫码长度
        private const val FAST_INPUT_THRESHOLD_MS = 30L // 红外线扫码枪快速输入阈值（毫秒）
        private const val INFRARED_SCAN_SPEED_THRESHOLD = 20L // 红外线扫码枪超快速输入阈值

        // 红外线扫码枪常见的前缀和后缀字符
        private val COMMON_PREFIXES = listOf("", "\u0002") // STX字符
        private val COMMON_SUFFIXES = listOf("", "\r", "\n", "\r\n", "\u0003") // CR, LF, ETX字符
    }

    private val _inputBuffer = MutableStateFlow("")
    val inputBuffer: StateFlow<String> = _inputBuffer.asStateFlow()

    private val _isReceivingInput = MutableStateFlow(false)
    val isReceivingInput: StateFlow<Boolean> = _isReceivingInput.asStateFlow()

    private val _scanMode = MutableStateFlow(ScanMode.CAMERA)
    val scanMode: StateFlow<ScanMode> = _scanMode.asStateFlow()

    private var inputJob: Job? = null
    private var lastInputTime = 0L
    private var inputStartTime = 0L
    private var onScanCompleted: ((String) -> Unit)? = null
    private var inputCharCount = 0
    private var isInfraredScanDetected = false

    enum class ScanMode {
        CAMERA,     // 摄像头扫码模式
        SCANNER_GUN // 扫码枪模式
    }

    /**
     * 设置扫码完成回调
     */
    fun setOnScanCompleted(callback: (String) -> Unit) {
        onScanCompleted = callback
    }

    /**
     * 切换扫码模式
     */
    fun setScanMode(mode: ScanMode) {
        _scanMode.value = mode
        clearInput()
        Log.d(TAG, "扫码模式切换为: $mode")
    }

    /**
     * 处理键盘输入字符
     * 针对红外线扫码枪优化
     */
    fun handleKeyInput(char: Char): Boolean {
        val currentTime = System.currentTimeMillis()

        // 只在扫码枪模式下处理输入
        if (_scanMode.value != ScanMode.SCANNER_GUN) {
            return false
        }

        // 检测是否为快速连续输入（扫码枪特征）
        if (_inputBuffer.value.isEmpty()) {
            inputStartTime = currentTime
            inputCharCount = 0
            isInfraredScanDetected = false
            _isReceivingInput.value = true
        }

        val timeSinceLastInput = currentTime - lastInputTime
        lastInputTime = currentTime
        inputCharCount++

        // 检测红外线扫码枪的超快速输入特征
        if (inputCharCount > 3 && timeSinceLastInput < INFRARED_SCAN_SPEED_THRESHOLD) {
            isInfraredScanDetected = true
            Log.d(TAG, "检测到红外线扫码枪输入模式")
        }

        // 如果输入间隔太长，重新开始
        if (timeSinceLastInput > INPUT_TIMEOUT_MS && _inputBuffer.value.isNotEmpty()) {
            Log.d(TAG, "输入超时，重新开始")
            clearInput()
            inputStartTime = currentTime
            return true
        }

        // 处理特殊字符（红外线扫码枪优化）
        when (char) {
            '\n', '\r' -> {
                // 回车键表示扫码完成
                handleScanComplete()
                return true
            }
            '\t' -> {
                // 忽略制表符
                return true
            }
            '\u0002' -> {
                // STX字符（某些红外线扫码枪的开始字符）
                Log.d(TAG, "检测到STX开始字符")
                return true
            }
            '\u0003' -> {
                // ETX字符（某些红外线扫码枪的结束字符）
                Log.d(TAG, "检测到ETX结束字符")
                handleScanComplete()
                return true
            }
            else -> {
                // 添加字符到缓冲区（扩展字符支持）
                if (char.isLetterOrDigit() ||
                    char in "!@#$%^&*()_+-=[]{}|;:,.<>?/~`\"'\\") {
                    addCharToBuffer(char)
                    return true
                } else if (char.code in 32..126) {
                    // 支持所有可打印ASCII字符
                    addCharToBuffer(char)
                    return true
                }
            }
        }

        return false
    }

    /**
     * 处理完整的输入字符串（用于测试或特殊情况）
     */
    fun handleStringInput(input: String) {
        if (_scanMode.value != ScanMode.SCANNER_GUN) {
            return
        }

        _inputBuffer.value = input.trim()
        if (_inputBuffer.value.isNotEmpty()) {
            handleScanComplete()
        }
    }

    /**
     * 添加字符到缓冲区
     */
    private fun addCharToBuffer(char: Char) {
        val currentBuffer = _inputBuffer.value
        
        // 检查长度限制
        if (currentBuffer.length >= MAX_SCAN_LENGTH) {
            Log.w(TAG, "输入长度超过限制，重新开始")
            clearInput()
            return
        }

        _inputBuffer.value = currentBuffer + char
        
        // 重置超时计时器
        resetTimeoutTimer()
        
        Log.d(TAG, "当前输入: ${_inputBuffer.value}")
    }

    /**
     * 处理扫码完成
     * 针对红外线扫码枪进行优化处理
     */
    private fun handleScanComplete() {
        var scannedData = _inputBuffer.value.trim()

        // 红外线扫码枪数据清理
        scannedData = cleanInfraredScanData(scannedData)

        if (isValidScanData(scannedData)) {
            val inputDuration = System.currentTimeMillis() - inputStartTime
            val scanType = if (isInfraredScanDetected) "红外线扫码枪" else "扫码枪"
            Log.d(TAG, "$scanType 扫码完成: $scannedData (耗时: ${inputDuration}ms, 字符数: $inputCharCount)")

            // 调用回调函数
            onScanCompleted?.invoke(scannedData)

            // 清理状态
            clearInput()
        } else {
            Log.w(TAG, "无效的扫码数据: $scannedData (原始: ${_inputBuffer.value})")
            // 对于红外线扫码枪，给予更多容错机会
            if (isInfraredScanDetected && scannedData.length >= MIN_SCAN_LENGTH - 1) {
                Log.d(TAG, "红外线扫码枪容错处理，尝试使用数据: $scannedData")
                onScanCompleted?.invoke(scannedData)
            }
            clearInput()
        }
    }

    /**
     * 清理红外线扫码枪数据
     * 移除常见的前缀和后缀字符
     */
    private fun cleanInfraredScanData(data: String): String {
        var cleanedData = data

        // 移除常见前缀
        COMMON_PREFIXES.forEach { prefix ->
            if (cleanedData.startsWith(prefix)) {
                cleanedData = cleanedData.removePrefix(prefix)
            }
        }

        // 移除常见后缀
        COMMON_SUFFIXES.forEach { suffix ->
            if (cleanedData.endsWith(suffix)) {
                cleanedData = cleanedData.removeSuffix(suffix)
            }
        }

        return cleanedData.trim()
    }

    /**
     * 验证扫码数据是否有效
     */
    private fun isValidScanData(data: String): Boolean {
        return data.length >= MIN_SCAN_LENGTH && 
               data.length <= MAX_SCAN_LENGTH &&
               data.isNotBlank()
    }

    /**
     * 重置超时计时器
     */
    private fun resetTimeoutTimer() {
        inputJob?.cancel()
        inputJob = CoroutineScope(Dispatchers.Main).launch {
            delay(INPUT_TIMEOUT_MS)
            if (_inputBuffer.value.isNotEmpty()) {
                Log.d(TAG, "输入超时，清除缓冲区")
                clearInput()
            }
        }
    }

    /**
     * 清除输入缓冲区
     */
    fun clearInput() {
        _inputBuffer.value = ""
        _isReceivingInput.value = false
        inputJob?.cancel()
        inputJob = null
        inputCharCount = 0
        isInfraredScanDetected = false
        Log.d(TAG, "输入缓冲区已清除")
    }

    /**
     * 获取当前输入状态信息
     */
    fun getInputStatus(): String {
        return when {
            _isReceivingInput.value && isInfraredScanDetected -> "正在接收红外线扫码枪输入..."
            _isReceivingInput.value -> "正在接收扫码枪输入..."
            _scanMode.value == ScanMode.SCANNER_GUN -> "等待扫码枪输入（支持红外线扫码枪）"
            else -> "摄像头扫码模式"
        }
    }

    /**
     * 检测是否为扫码枪输入模式
     * 基于输入速度和模式判断
     */
    fun detectScannerGunInput(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceStart = currentTime - inputStartTime
        val inputLength = _inputBuffer.value.length
        
        // 如果在短时间内输入了多个字符，可能是扫码枪
        return inputLength > 3 && timeSinceStart < (inputLength * FAST_INPUT_THRESHOLD_MS)
    }

    /**
     * 释放资源
     */
    fun release() {
        inputJob?.cancel()
        onScanCompleted = null
        clearInput()
        Log.d(TAG, "ScannerGunHandler 资源已释放")
    }
}
